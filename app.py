
from flask import Flask
from flask_socketio import SocketIO
from app.routes import main

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.register_blueprint(main)

# Initialize Socket.IO
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# Import socket events after socketio is created
from app.socket_events import register_socket_events
register_socket_events(socketio)

if __name__ == '__main__':
	socketio.run(app, debug=True, host='0.0.0.0', port=5000)
