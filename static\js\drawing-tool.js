// Rectangle Drawing Tool for TradingView Lightweight Charts
// Based on the TradingView plugin examples
//
// Enhanced to use crosshair crossover points as reference for drawing rectangles
// instead of raw mouse cursor position. This provides more accurate positioning
// especially when crosshair is in magnet mode and snaps to data points.


// Helper function for positioning
function positionsBox(p1, p2, pixelRatio) {
    const start = Math.min(p1, p2) * pixelRatio;
    const end = Math.max(p1, p2) * pixelRatio;
    return {
        position: Math.round(start),
        length: Math.round(end - start)
    };
}

// Ensure defined utility
function ensureDefined(value) {
    if (value === null || value === undefined) {
        throw new Error('Value is null or undefined');
    }
    return value;
}

// Plugin base class
class PluginBase {
    constructor() {
        this._chart = null;
        this._series = null;
        this._requestUpdate = null;
    }

    attached(param) {
        this._chart = param.chart;
        this._series = param.series;
        this._requestUpdate = param.requestUpdate;
        this.requestUpdate();
    }

    detached() {
        this._chart = null;
        this._series = null;
        this._requestUpdate = null;
    }

    get chart() {
        return ensureDefined(this._chart);
    }

    get series() {
        return ensureDefined(this._series);
    }

    requestUpdate() {
        if (this._requestUpdate) {
            this._requestUpdate();
        }
    }

    updateAllViews() {
        this.requestUpdate();
    }

    priceAxisViews() {
        return [];
    }

    timeAxisViews() {
        return [];
    }

    paneViews() {
        return [];
    }

    priceAxisPaneViews() {
        return [];
    }

    timeAxisPaneViews() {
        return [];
    }
}

// Rectangle renderer for the main pane
class RectanglePaneRenderer {
    constructor(p1, p2, fillColor, isSelected = false) {
        this._p1 = p1;
        this._p2 = p2;
        this._fillColor = fillColor;
        this._isSelected = isSelected;
    }

    draw(target) {
        target.useBitmapCoordinateSpace(scope => {
            if (
                this._p1.x === null ||
                this._p1.y === null ||
                this._p2.x === null ||
                this._p2.y === null
            ) return;

            const ctx = scope.context;
            const horizontalPositions = positionsBox(
                this._p1.x,
                this._p2.x,
                scope.horizontalPixelRatio
            );
            const verticalPositions = positionsBox(
                this._p1.y,
                this._p2.y,
                scope.verticalPixelRatio
            );

            // Draw filled rectangle
            ctx.fillStyle = this._fillColor;
            ctx.fillRect(
                horizontalPositions.position,
                verticalPositions.position,
                horizontalPositions.length,
                verticalPositions.length
            );

            // Draw selection highlight and resize handles if selected
            if (this._isSelected) {
                this._drawSelectionHighlight(ctx, horizontalPositions, verticalPositions, scope);
            }
        });
    }

    _drawSelectionHighlight(ctx, hPos, vPos, scope) {
        const { position: x, length: width } = hPos;
        const { position: y, length: height } = vPos;
        const pixelRatio = scope.horizontalPixelRatio;

        // Draw selection border
        ctx.strokeStyle = '#2962FF';
        ctx.lineWidth = 2 * pixelRatio;
        ctx.setLineDash([5 * pixelRatio, 5 * pixelRatio]);
        ctx.strokeRect(x, y, width, height);
        ctx.setLineDash([]);

        // Draw resize handles
        const handleSize = 8 * pixelRatio;
        const halfHandle = handleSize * 0.5;
        const midX = x + width * 0.5;
        const midY = y + height * 0.5;
        const rightX = x + width;
        const bottomY = y + height;

        const handles = [
            [x - halfHandle, y - halfHandle],
            [midX - halfHandle, y - halfHandle],
            [rightX - halfHandle, y - halfHandle],
            [x - halfHandle, midY - halfHandle],
            [rightX - halfHandle, midY - halfHandle],
            [x - halfHandle, bottomY - halfHandle],
            [midX - halfHandle, bottomY - halfHandle],
            [rightX - halfHandle, bottomY - halfHandle]
        ];

        ctx.fillStyle = '#2962FF';
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = pixelRatio;

        for (const [hx, hy] of handles) {
            ctx.fillRect(hx, hy, handleSize, handleSize);
            ctx.strokeRect(hx, hy, handleSize, handleSize);
        }
    }
}

// Rectangle pane view
class RectanglePaneView {
    constructor(source) {
        this._source = source;
        this._p1 = { x: null, y: null };
        this._p2 = { x: null, y: null };
    }

    update() {
        try {
            const series = this._source.series;
            const chart = this._source.chart;

            if (!series || !chart) {
                // Set to null if series/chart not available
                this._p1 = { x: null, y: null };
                this._p2 = { x: null, y: null };
                return;
            }

            const y1 = series.priceToCoordinate(this._source._p1.price);
            const y2 = series.priceToCoordinate(this._source._p2.price);
            const timeScale = chart.timeScale();
            const x1 = timeScale.timeToCoordinate(this._source._p1.time);
            const x2 = timeScale.timeToCoordinate(this._source._p2.time);
            this._p1 = { x: x1, y: y1 };
            this._p2 = { x: x2, y: y2 };
        } catch (error) {
            // Handle case where series/chart is no longer available
            this._p1 = { x: null, y: null };
            this._p2 = { x: null, y: null };
        }
    }

    renderer() {
        return new RectanglePaneRenderer(
            this._p1,
            this._p2,
            this._source._options.fillColor,
            this._source._selected
        );
    }
}

// Removed axis highlighting classes - no longer needed

// Default options for the rectangle drawing tool
const defaultOptions = {
    fillColor: 'rgba(200, 50, 100, 0.75)',
    previewFillColor: 'rgba(200, 50, 100, 0.25)',
    labelColor: 'rgba(200, 50, 100, 1)',
    labelTextColor: 'white',
    showLabels: true,
    priceLabelFormatter: (price) => price.toFixed(2),
    timeLabelFormatter: (time) => {
        if (typeof time === 'string') return time;
        // Handle business day format
        if (time && typeof time === 'object' && time.year) {
            const date = new Date(time.year, time.month - 1, time.day);
            return date.toLocaleDateString();
        }
        // Handle timestamp
        const date = new Date(time * 1000);
        return date.toLocaleDateString();
    },
};

// Main Rectangle class
class Rectangle extends PluginBase {
    constructor(p1, p2, options = {}) {
        super();
        this._p1 = p1;
        this._p2 = p2;
        this._options = {
            ...defaultOptions,
            ...options,
        };
        this._paneViews = [new RectanglePaneView(this)];
        this._selected = false;
        this._id = Math.random().toString(36).substring(2, 11); // Unique ID
    }

    updateAllViews() {
        try {
            this._paneViews.forEach(pw => pw.update());
        } catch (error) {
            // Silently handle detached rectangles
        }
    }

    paneViews() {
        return this._paneViews;
    }

    applyOptions(options) {
        this._options = { ...this._options, ...options };
        this.requestUpdate();
    }

    setSelected(selected) {
        this._selected = selected;
        // Update all views to reflect selection state
        this.updateAllViews();
        this.requestUpdate();
    }

    isSelected() {
        return this._selected;
    }

    getId() {
        return this._id;
    }

    // Check if a point is inside the rectangle
    containsPoint(time, price) {
        const minTime = Math.min(this._p1.time, this._p2.time);
        const maxTime = Math.max(this._p1.time, this._p2.time);
        const minPrice = Math.min(this._p1.price, this._p2.price);
        const maxPrice = Math.max(this._p1.price, this._p2.price);

        return time >= minTime && time <= maxTime && price >= minPrice && price <= maxPrice;
    }

    // Update rectangle points
    updatePoints(p1, p2) {
        this._p1 = p1;
        this._p2 = p2;
        this.updateAllViews();
        this.requestUpdate();
    }

    // Efficient live resize update (similar to preview updateEndPoint)
    liveResizeUpdate(p1, p2) {
        this._p1 = p1;
        this._p2 = p2;
        // Only update the main pane view for performance
        if (this._paneViews[0]) {
            this._paneViews[0].update();
        }
        this.requestUpdate();
    }

    // Get resize handle positions
    getResizeHandles() {
        const minTime = Math.min(this._p1.time, this._p2.time);
        const maxTime = Math.max(this._p1.time, this._p2.time);
        const minPrice = Math.min(this._p1.price, this._p2.price);
        const maxPrice = Math.max(this._p1.price, this._p2.price);
        const midTime = (minTime + maxTime) / 2;
        const midPrice = (minPrice + maxPrice) / 2;

        return {
            'top-left': { time: minTime, price: maxPrice },
            'top-center': { time: midTime, price: maxPrice },
            'top-right': { time: maxTime, price: maxPrice },
            'middle-left': { time: minTime, price: midPrice },
            'middle-right': { time: maxTime, price: midPrice },
            'bottom-left': { time: minTime, price: minPrice },
            'bottom-center': { time: midTime, price: minPrice },
            'bottom-right': { time: maxTime, price: minPrice }
        };
    }

    getResizeHandle(time, price, tolerance = 0.02) {
        if (!this._selected) return null;

        const handles = this.getResizeHandles();
        const timeRange = Math.abs(this._p2.time - this._p1.time);
        const priceRange = Math.abs(this._p2.price - this._p1.price);

        const timeTolerance = timeRange * tolerance;
        const priceTolerance = priceRange * tolerance;

        for (const [handleName, handlePos] of Object.entries(handles)) {
            if (Math.abs(time - handlePos.time) <= timeTolerance &&
                Math.abs(price - handlePos.price) <= priceTolerance) {
                return handleName;
            }
        }
        return null;
    }

    // Fast live resize for smooth dragging (like drawing preview)
    liveResizeToHandle(handleName, newTime, newPrice) {
        const [minTime, maxTime] = this._p1.time < this._p2.time ?
            [this._p1.time, this._p2.time] : [this._p2.time, this._p1.time];
        const [minPrice, maxPrice] = this._p1.price < this._p2.price ?
            [this._p1.price, this._p2.price] : [this._p2.price, this._p1.price];

        const resizeMap = {
            'top-left': [newTime, newPrice, maxTime, minPrice],
            'top-center': [minTime, newPrice, maxTime, minPrice],
            'top-right': [minTime, newPrice, newTime, minPrice],
            'middle-left': [newTime, maxPrice, maxTime, minPrice],
            'middle-right': [minTime, maxPrice, newTime, minPrice],
            'bottom-left': [newTime, maxPrice, maxTime, newPrice],
            'bottom-center': [minTime, maxPrice, maxTime, newPrice],
            'bottom-right': [minTime, maxPrice, newTime, newPrice]
        };

        const coords = resizeMap[handleName];
        if (coords) {
            this.liveResizeUpdate(
                { time: coords[0], price: coords[1] },
                { time: coords[2], price: coords[3] }
            );
        }
    }

    // Final resize update (full update for completion)
    resizeToHandle(handleName, newTime, newPrice) {
        const [minTime, maxTime] = this._p1.time < this._p2.time ?
            [this._p1.time, this._p2.time] : [this._p2.time, this._p1.time];
        const [minPrice, maxPrice] = this._p1.price < this._p2.price ?
            [this._p1.price, this._p2.price] : [this._p2.price, this._p1.price];

        const resizeMap = {
            'top-left': [newTime, newPrice, maxTime, minPrice],
            'top-center': [minTime, newPrice, maxTime, minPrice],
            'top-right': [minTime, newPrice, newTime, minPrice],
            'middle-left': [newTime, maxPrice, maxTime, minPrice],
            'middle-right': [minTime, maxPrice, newTime, minPrice],
            'bottom-left': [newTime, maxPrice, maxTime, newPrice],
            'bottom-center': [minTime, maxPrice, maxTime, newPrice],
            'bottom-right': [minTime, maxPrice, newTime, newPrice]
        };

        const coords = resizeMap[handleName];
        if (coords) {
            this.updatePoints(
                { time: coords[0], price: coords[1] },
                { time: coords[2], price: coords[3] }
            );
        }
    }
}

// Preview Rectangle class for live drawing
class PreviewRectangle extends Rectangle {
    constructor(p1, p2, options = {}) {
        super(p1, p2, options);
        this._options.fillColor = this._options.previewFillColor;
    }

    updateEndPoint(p) {
        this._p2 = p;
        this._paneViews[0].update();
        this.requestUpdate();
    }
}

// Main Rectangle Drawing Tool class
export class RectangleDrawingTool {
    constructor(chart, series, options = {}) {
        this._chart = chart;
        this._series = series;
        this._defaultOptions = options;
        this._rectangles = [];
        this._previewRectangle = null;
        this._points = [];
        this._drawing = false;
        this._selectedRectangle = null;
        this._resizing = false;
        this._resizeHandle = null;
        this._toolbox = null;
        this._extendedRectangles = new Set(); // Track rectangles that are extended to the right

        // Store current crosshair position for drawing reference
        this._currentCrosshairPosition = null;

        this._chart.subscribeClick(this._clickHandler);
        this._chart.subscribeCrosshairMove(this._moveHandler);

        // Subscribe to visible range changes to update extended rectangles
        this._visibleRangeHandler = () => this._updateExtendedRectangles();
        this._chart.timeScale().subscribeVisibleTimeRangeChange(this._visibleRangeHandler);

        // Add direct mouse event listener for Alt+Click detection
        this._setupMouseEventHandler();

        // Add keyboard event listeners
        this._setupKeyboardHandlers();
    }

    _clickHandler = (param) => this._onClick(param);
    _moveHandler = (param) => this._onMouseMove(param);

    /**
     * Helper method to extract crosshair position from crosshair move/click param
     * Prioritizes series data points (when crosshair is in magnet mode) over raw coordinates
     * This ensures rectangles are drawn using the exact crosshair crossover points
     * @param {object} param - The parameter object from crosshair move/click events
     * @returns {object|null} - Object with {time, price} or null if invalid
     */
    _getCrosshairPosition(param) {
        if (!param.point || !param.time || !this._series) return null;

        let time = param.time;
        let price = null;

        // Check if crosshair is snapped to a data point (magnet mode)
        if (param.seriesData && param.seriesData.has(this._series)) {
            const dataPoint = param.seriesData.get(this._series);
            if (dataPoint && dataPoint.time !== undefined) {
                time = dataPoint.time;
                // Use the appropriate price based on data point type
                if (dataPoint.close !== undefined) {
                    price = dataPoint.close; // For candlestick data
                } else if (dataPoint.value !== undefined) {
                    price = dataPoint.value; // For line series data
                } else if (dataPoint.high !== undefined) {
                    price = dataPoint.high; // Fallback to high for candlestick
                }
            }
        }

        // Fallback to coordinate-based price if no series data available
        if (price === null) {
            price = this._series.coordinateToPrice(param.point.y);
            if (price === null) return null;
        }

        return { time, price };
    }

    remove() {
        this.stopDrawing();
        if (this._chart) {
            this._chart.unsubscribeClick(this._clickHandler);
            this._chart.unsubscribeCrosshairMove(this._moveHandler);
            if (this._visibleRangeHandler) {
                this._chart.timeScale().unsubscribeVisibleTimeRangeChange(this._visibleRangeHandler);
            }
        }
        this._rectangles.forEach(rectangle => {
            this._removeRectangle(rectangle);
        });
        this._rectangles = [];
        this._removePreviewRectangle();
        this._hideToolbox();

        // Remove keyboard handlers
        if (this._keydownHandler) {
            document.removeEventListener('keydown', this._keydownHandler);
        }

        // Remove mouse event handlers
        const chartElement = this._chart?.chartElement();
        if (chartElement) {
            if (this._mouseClickHandler) {
                chartElement.removeEventListener('click', this._mouseClickHandler, true);
            }
            if (this._mouseDownHandler) {
                chartElement.removeEventListener('mousedown', this._mouseDownHandler, true);
            }
            if (this._mouseMoveHandler) {
                chartElement.removeEventListener('mousemove', this._mouseMoveHandler, true);
            }
            if (this._mouseUpHandler) {
                chartElement.removeEventListener('mouseup', this._mouseUpHandler, true);
            }
        }



        this._chart = null;
        this._series = null;
    }

    startDrawing() {
        this._drawing = true;
        this._points = [];
    }

    stopDrawing() {
        this._drawing = false;
        this._points = [];
        this._removePreviewRectangle();
    }

    isDrawing() {
        return this._drawing;
    }

    _onClick(param) {
        const position = this._getCrosshairPosition(param);
        if (!position) return;

        const { time, price } = position;
        const isCtrlClick = (param.originalEvent?.ctrlKey) || (param.ctrlKey);

        if (isCtrlClick) {
            this._handleRectangleSelection(time, price);
            return;
        }

        if (this._selectedRectangle && !this._drawing) {
            const handle = this._selectedRectangle.getResizeHandle(time, price);
            if (handle) {
                this._startResize(handle);
                return;
            }
        }

        if (this._drawing) {
            this._addPoint({ time, price });
            return;
        }

        if (this._selectedRectangle) {
            this._deselectRectangle();
        }
    }

    _onMouseMove(param) {
        const position = this._getCrosshairPosition(param);
        if (!position) return;

        const { time, price } = position;

        // Store current crosshair position for drawing reference
        this._currentCrosshairPosition = { time, price };

        if (this._drawing && this._previewRectangle) {
            this._previewRectangle.updateEndPoint({ time, price });
            return;
        }

        if (this._resizing && this._selectedRectangle && this._resizeHandle) {
            this._selectedRectangle.liveResizeToHandle(this._resizeHandle, time, price);
            return;
        }

        if (this._selectedRectangle && !this._drawing && !this._resizing) {
            const handle = this._selectedRectangle.getResizeHandle(time, price);
            this._updateCursor(handle);
        }
    }

    _addPoint(p) {
        this._points.push(p);
        if (this._points.length >= 2) {
            this._addNewRectangle(this._points[0], this._points[1]);
            this.stopDrawing();
            this._removePreviewRectangle();

            // Auto-disable drawing mode and update navbar button
            this._updateNavbarButton(false);
        }
        if (this._points.length === 1) {
            this._addPreviewRectangle(this._points[0]);
        }
    }

    _updateNavbarButton(isDrawing) {
        const drawingToolBtn = document.getElementById('drawing-tool-btn');
        if (drawingToolBtn) {
            if (isDrawing) {
                drawingToolBtn.classList.add('active');
                drawingToolBtn.style.background = '#2962FF';
            } else {
                drawingToolBtn.classList.remove('active');
                drawingToolBtn.style.background = '';
            }
        }
    }

    _handleRectangleSelection(time, price) {
        const clickedRectangle = this._rectangles.find(rect => rect.containsPoint(time, price));

        if (clickedRectangle) {
            this._selectRectangle(clickedRectangle);
        } else {
            this._deselectRectangle();
        }
    }

    _selectRectangle(rectangle) {
        if (this._selectedRectangle) {
            this._selectedRectangle.setSelected(false);
        }

        this._selectedRectangle = rectangle;
        rectangle.setSelected(true);
        this._showToolbox(rectangle);
    }

    _deselectRectangle() {
        if (this._selectedRectangle) {
            this._selectedRectangle.setSelected(false);
            this._selectedRectangle = null;
        }
        this._hideToolbox();
    }

    _addNewRectangle(p1, p2) {
        const rectangle = new Rectangle(p1, p2, { ...this._defaultOptions });
        this._rectangles.push(rectangle);
        ensureDefined(this._series).attachPrimitive(rectangle);
    }

    _removeRectangle(rectangle) {
        ensureDefined(this._series).detachPrimitive(rectangle);
    }

    _addPreviewRectangle(p) {
        this._previewRectangle = new PreviewRectangle(p, p, {
            ...this._defaultOptions,
        });
        ensureDefined(this._series).attachPrimitive(this._previewRectangle);
    }

    _removePreviewRectangle() {
        if (this._previewRectangle) {
            ensureDefined(this._series).detachPrimitive(this._previewRectangle);
            this._previewRectangle = null;
        }
    }

    _showToolbox(rectangle) {
        this._hideToolbox(); // Remove existing toolbox

        // Create toolbox container - horizontal toolbar style
        this._toolbox = document.createElement('div');
        this._toolbox.style.cssText = `
            position: absolute;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(35, 38, 47, 0.95);
            border: 1px solid #363C4E;
            border-radius: 6px;
            padding: 6px;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 4px;
            backdrop-filter: blur(4px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        `;

        // Color picker button
        const colorBtn = document.createElement('div');
        colorBtn.style.cssText = `
            width: 28px;
            height: 28px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
            position: relative;
        `;

        // Color picker icon (palette icon)
        colorBtn.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" style="color: #B2B5BE;">
                <path d="M12 2C17.5 2 22 6.5 22 12C22 13.8 21.2 15.5 20 16.7L18.3 15C19.3 14.2 20 13.2 20 12C20 7.6 16.4 4 12 4C7.6 4 4 7.6 4 12C4 16.4 7.6 20 12 20H16V22H12C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2Z" fill="currentColor"/>
                <circle cx="8.5" cy="8.5" r="1.5" fill="currentColor"/>
                <circle cx="15.5" cy="8.5" r="1.5" fill="currentColor"/>
                <circle cx="8.5" cy="15.5" r="1.5" fill="currentColor"/>
                <circle cx="15.5" cy="15.5" r="1.5" fill="currentColor"/>
                <circle cx="12" cy="12" r="1.5" fill="currentColor"/>
            </svg>
        `;

        colorBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this._showColorPicker(rectangle, colorBtn);
        });

        colorBtn.addEventListener('mouseenter', () => {
            colorBtn.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
        });

        colorBtn.addEventListener('mouseleave', () => {
            colorBtn.style.backgroundColor = 'transparent';
        });

        this._toolbox.appendChild(colorBtn);

        // Separator
        const separator = document.createElement('div');
        separator.style.cssText = `
            width: 1px;
            height: 20px;
            background: #363C4E;
            margin: 0 2px;
        `;
        this._toolbox.appendChild(separator);

        // Extend right button
        const extendBtn = document.createElement('div');
        extendBtn.style.cssText = `
            width: 28px;
            height: 28px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        `;

        extendBtn.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <rect x="2" y="4" width="8" height="8" stroke="#B2B5BE" stroke-width="1" fill="none"/>
                <path d="M10 6L14 8L10 10V9H8V7H10V6Z" fill="#2962FF"/>
                <line x1="14" y1="2" x2="14" y2="14" stroke="#2962FF" stroke-width="2"/>
            </svg>
        `;
        extendBtn.title = 'Extend to Last Visible Bar';

        extendBtn.addEventListener('mouseenter', () => {
            extendBtn.style.backgroundColor = 'rgba(41, 98, 255, 0.2)';
        });

        extendBtn.addEventListener('mouseleave', () => {
            extendBtn.style.backgroundColor = 'transparent';
        });

        extendBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this._extendRectangleRight(rectangle);
        });

        this._toolbox.appendChild(extendBtn);

        // Separator before delete
        const separator2 = document.createElement('div');
        separator2.style.cssText = `
            width: 1px;
            height: 20px;
            background: #363C4E;
            margin: 0 2px;
        `;
        this._toolbox.appendChild(separator2);

        // Delete button
        const deleteBtn = document.createElement('div');
        deleteBtn.style.cssText = `
            width: 28px;
            height: 28px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        `;

        deleteBtn.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" style="color: #F23645;">
                <path d="M6 19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V7H6V19ZM19 4H15.5L14.5 3H9.5L8.5 4H5V6H19V4Z"/>
            </svg>
        `;

        deleteBtn.addEventListener('mouseenter', () => {
            deleteBtn.style.backgroundColor = 'rgba(242, 54, 69, 0.2)';
        });

        deleteBtn.addEventListener('mouseleave', () => {
            deleteBtn.style.backgroundColor = 'transparent';
        });

        deleteBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this._deleteRectangle(rectangle);
        });

        this._toolbox.appendChild(deleteBtn);

        const chartElement = this._chart.chartElement();
        const chartContainer = chartElement?.parentElement;

        if (chartContainer) {
            chartContainer.appendChild(this._toolbox);
        } else {
            document.body.appendChild(this._toolbox);
        }
    }

    _hideToolbox() {
        if (this._toolbox) {
            this._toolbox.remove();
            this._toolbox = null;
        }
        this._hideColorPicker();
    }

    _showColorPicker(rectangle, anchorElement) {
        // Hide any existing color picker
        this._hideColorPicker();

        // Create color picker popup
        this._colorPickerPopup = document.createElement('div');
        this._colorPickerPopup.style.cssText = `
            position: absolute;
            background: #2A2D3A;
            border: 1px solid #363C4E;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
            z-index: 10000;
            min-width: 200px;
        `;

        // Color palette
        const colorGrid = document.createElement('div');
        colorGrid.style.cssText = `
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: 4px;
            margin-bottom: 12px;
        `;

        // Define color palette (exact colors from your image)
        const colors = [
            // Row 1 - Grays
            '#ffffff', '#e6e6e6', '#cccccc', '#b3b3b3', '#999999', '#808080', '#666666', '#4d4d4d', '#333333', '#000000',
            // Row 2 - Primary colors
            '#f23645', '#ff8000', '#ffff00', '#80ff00', '#00ff80', '#00ffff', '#0080ff', '#8000ff', '#ff00ff', '#ff0080',
            // Row 3 - Light pastels
            '#fccbcd', '#ffe6cc', '#ffffe6', '#e6ffe6', '#e6ffff', '#e6f3ff', '#e6e6ff', '#f3e6ff', '#ffe6ff', '#ffe6f3',
            // Row 4 - Medium tones
            '#faa1a4', '#ffcc99', '#ffffcc', '#ccffcc', '#ccffff', '#cce6ff', '#ccccff', '#e6ccff', '#ffccff', '#ffcce6',
            // Row 5 - Darker tones
            '#f77c80', '#ff9966', '#ffff99', '#99ff99', '#99ffff', '#99ccff', '#9999ff', '#cc99ff', '#ff99ff', '#ff99cc',
            // Row 6 - Deep colors
            '#f7525f', '#ff6633', '#ffff66', '#66ff66', '#66ffff', '#6699ff', '#6666ff', '#9966ff', '#ff66ff', '#ff6699',
            // Row 7 - Rich colors
            '#b22833', '#ff3300', '#ffff33', '#33ff33', '#33ffff', '#3366ff', '#3333ff', '#6633ff', '#ff33ff', '#ff3366',
            // Row 8 - Saturated colors
            '#801922', '#cc3300', '#cccc00', '#00cc00', '#00cccc', '#0066cc', '#0000cc', '#3300cc', '#cc00cc', '#cc0066'
        ];

        colors.forEach(color => {
            const colorSwatch = document.createElement('div');
            colorSwatch.style.cssText = `
                width: 16px;
                height: 16px;
                background-color: ${color};
                border: 1px solid #555;
                border-radius: 2px;
                cursor: pointer;
                transition: transform 0.1s;
            `;

            colorSwatch.addEventListener('mouseenter', () => {
                colorSwatch.style.transform = 'scale(1.1)';
            });

            colorSwatch.addEventListener('mouseleave', () => {
                colorSwatch.style.transform = 'scale(1)';
            });

            colorSwatch.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this._applyColorWithOpacity(rectangle, color);
            });

            colorGrid.appendChild(colorSwatch);
        });

        this._colorPickerPopup.appendChild(colorGrid);

        // Opacity section
        const opacitySection = document.createElement('div');
        opacitySection.style.cssText = `
            border-top: 1px solid #363C4E;
            padding-top: 12px;
        `;

        const opacityLabel = document.createElement('div');
        opacityLabel.textContent = 'Opacity';
        opacityLabel.style.cssText = `
            color: #B2B5BE;
            font-size: 12px;
            margin-bottom: 8px;
        `;

        const opacityContainer = document.createElement('div');
        opacityContainer.style.cssText = `
            display: flex;
            align-items: center;
            gap: 8px;
        `;

        const opacitySlider = document.createElement('input');
        opacitySlider.type = 'range';
        opacitySlider.min = '0';
        opacitySlider.max = '100';
        opacitySlider.value = this._getCurrentOpacity(rectangle);
        opacitySlider.style.cssText = `
            flex: 1;
            height: 4px;
            background: linear-gradient(to right, #363C4E, #2962FF);
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
        `;

        // Style the slider thumb
        const style = document.createElement('style');
        style.textContent = `
            input[type="range"]::-webkit-slider-thumb {
                -webkit-appearance: none;
                width: 16px;
                height: 16px;
                background: #2962FF;
                border-radius: 50%;
                cursor: pointer;
            }
            input[type="range"]::-moz-range-thumb {
                width: 16px;
                height: 16px;
                background: #2962FF;
                border-radius: 50%;
                cursor: pointer;
                border: none;
            }
        `;
        document.head.appendChild(style);

        const opacityValue = document.createElement('div');
        opacityValue.textContent = opacitySlider.value + '%';
        opacityValue.style.cssText = `
            color: #B2B5BE;
            font-size: 12px;
            min-width: 35px;
        `;

        opacitySlider.addEventListener('input', (e) => {
            opacityValue.textContent = e.target.value + '%';
            this._currentOpacity = parseInt(e.target.value) / 100;
            this._applyCurrentColorWithOpacity(rectangle);
        });

        opacityContainer.appendChild(opacitySlider);
        opacityContainer.appendChild(opacityValue);
        opacitySection.appendChild(opacityLabel);
        opacitySection.appendChild(opacityContainer);
        this._colorPickerPopup.appendChild(opacitySection);

        // Position the popup
        const rect = anchorElement.getBoundingClientRect();
        this._colorPickerPopup.style.left = rect.left + 'px';
        this._colorPickerPopup.style.top = (rect.bottom + 8) + 'px';

        // Add to document
        document.body.appendChild(this._colorPickerPopup);

        // Store current color for opacity changes
        this._currentColor = this._extractColorFromFill(rectangle._options.fillColor);
        this._currentOpacity = this._getCurrentOpacity(rectangle) / 100;

        // Close on outside click
        setTimeout(() => {
            document.addEventListener('click', this._closeColorPickerOnOutsideClick.bind(this), { once: true });
        }, 100);
    }

    _hideColorPicker() {
        if (this._colorPickerPopup) {
            this._colorPickerPopup.remove();
            this._colorPickerPopup = null;
        }
    }

    _closeColorPickerOnOutsideClick(e) {
        if (this._colorPickerPopup && !this._colorPickerPopup.contains(e.target)) {
            this._hideColorPicker();
        }
    }

    _getCurrentOpacity(rectangle) {
        const fillColor = rectangle._options.fillColor;
        const match = fillColor.match(/rgba?\([^,]+,[^,]+,[^,]+,\s*([^)]+)\)/);
        return match ? Math.round(parseFloat(match[1]) * 100) : 30;
    }

    _applyColorWithOpacity(rectangle, color) {
        this._currentColor = color;
        this._applyCurrentColorWithOpacity(rectangle);
    }

    _applyCurrentColorWithOpacity(rectangle) {
        const opacity = this._currentOpacity || 0.3;
        const hex = this._currentColor.replace('#', '');
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);

        rectangle.applyOptions({
            fillColor: `rgba(${r}, ${g}, ${b}, ${opacity})`
        });
    }

    _extractColorFromFill(fillColor) {
        // Extract hex color from rgba string
        const match = fillColor.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
        if (match) {
            const r = parseInt(match[1]).toString(16).padStart(2, '0');
            const g = parseInt(match[2]).toString(16).padStart(2, '0');
            const b = parseInt(match[3]).toString(16).padStart(2, '0');
            return `#${r}${g}${b}`;
        }
        return '#6495ED';
    }

    _changeRectangleColor(rectangle, color) {
        const r = parseInt(color.substring(1, 3), 16);
        const g = parseInt(color.substring(3, 5), 16);
        const b = parseInt(color.substring(5, 7), 16);

        rectangle.applyOptions({
            fillColor: `rgba(${r}, ${g}, ${b}, ${rectangle.isSelected() ? 0.5 : 0.3})`,
            labelColor: color
        });
    }

    _extendRectangleRight(rectangle) {
        try {
            const timeScale = this._chart.timeScale();

            // Get the visible time range
            const visibleRange = timeScale.getVisibleRange();
            if (!visibleRange) return;

            // Get the rightmost visible time (last bar)
            const lastVisibleTime = visibleRange.to;

            // Get current rectangle bounds
            const currentMaxTime = Math.max(rectangle._p1.time, rectangle._p2.time);

            // Only extend if the rectangle's right edge is not already at or beyond the last visible bar
            if (currentMaxTime >= lastVisibleTime) return;

            // Extend right edge to the last visible bar
            const newMaxTime = lastVisibleTime;

            // Mark this rectangle as extended
            this._extendedRectangles.add(rectangle);

            // Update rectangle points maintaining the same orientation
            if (rectangle._p1.time === currentMaxTime) {
                // p1 is the right edge
                rectangle.updatePoints(
                    { time: newMaxTime, price: rectangle._p1.price },
                    rectangle._p2
                );
            } else {
                // p2 is the right edge
                rectangle.updatePoints(
                    rectangle._p1,
                    { time: newMaxTime, price: rectangle._p2.price }
                );
            }

        } catch (error) {
            // Silently handle errors
        }
    }

    _updateExtendedRectangles() {
        // Update all rectangles that are marked as extended to the right
        if (this._extendedRectangles.size === 0) return;

        try {
            const timeScale = this._chart.timeScale();
            const visibleRange = timeScale.getVisibleRange();
            if (!visibleRange) return;

            const newLastVisibleTime = visibleRange.to;

            this._extendedRectangles.forEach(rectangle => {
                // Check if rectangle still exists
                if (!this._rectangles.includes(rectangle)) {
                    this._extendedRectangles.delete(rectangle);
                    return;
                }

                // Get current rectangle bounds
                const currentMaxTime = Math.max(rectangle._p1.time, rectangle._p2.time);

                // Only update if the new visible range extends further
                if (newLastVisibleTime > currentMaxTime) {
                    // Update rectangle points maintaining the same orientation
                    if (rectangle._p1.time === currentMaxTime) {
                        // p1 is the right edge
                        rectangle.updatePoints(
                            { time: newLastVisibleTime, price: rectangle._p1.price },
                            rectangle._p2
                        );
                    } else {
                        // p2 is the right edge
                        rectangle.updatePoints(
                            rectangle._p1,
                            { time: newLastVisibleTime, price: rectangle._p2.price }
                        );
                    }
                }
            });
        } catch (error) {
            // Silently handle errors
        }
    }

    _deleteRectangle(rectangle) {
        const index = this._rectangles.indexOf(rectangle);
        if (index > -1) {
            // Deselect first, before removing from chart
            if (this._selectedRectangle === rectangle) {
                this._selectedRectangle = null;
                this._hideToolbox();
            }

            // Remove from extended rectangles set
            this._extendedRectangles.delete(rectangle);

            // Remove from array and chart
            this._rectangles.splice(index, 1);
            this._removeRectangle(rectangle);
        }
    }

    _setupMouseEventHandler() {
        // Get the chart's DOM element
        const chartElement = this._chart.chartElement();
        if (!chartElement) return;

        this._mouseClickHandler = (e) => {
            if (!e.ctrlKey) return;

            e.preventDefault();
            e.stopPropagation();

            const rect = chartElement.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const timeScale = this._chart.timeScale();
            const time = timeScale.coordinateToTime(x);
            const price = this._series.coordinateToPrice(y);

            if (time !== null && price !== null) {
                this._handleRectangleSelection(time, price);
            }
        };

        this._mouseDownHandler = (e) => {
            if (!this._selectedRectangle || this._drawing) return;

            const rect = chartElement.getBoundingClientRect();
            const timeScale = this._chart.timeScale();
            const time = timeScale.coordinateToTime(e.clientX - rect.left);
            const price = this._series.coordinateToPrice(e.clientY - rect.top);

            if (time !== null && price !== null) {
                // Use current crosshair position if available for more accurate handle detection
                let currentTime = time;
                let currentPrice = price;
                if (this._currentCrosshairPosition && this._currentCrosshairPosition.time && this._currentCrosshairPosition.price !== null) {
                    currentTime = this._currentCrosshairPosition.time;
                    currentPrice = this._currentCrosshairPosition.price;
                }

                const handle = this._selectedRectangle.getResizeHandle(currentTime, currentPrice);
                if (handle) {
                    e.preventDefault();
                    e.stopPropagation();
                    this._startResize(handle);
                    this._disableChartInteractions();
                }
            }
        };

        this._mouseMoveHandler = (e) => {
            if (this._selectedRectangle && !this._drawing && !this._resizing) {
                // Handle cursor updates for resize handles
                const rect = chartElement.getBoundingClientRect();
                const timeScale = this._chart.timeScale();
                const time = timeScale.coordinateToTime(e.clientX - rect.left);
                const price = this._series.coordinateToPrice(e.clientY - rect.top);

                if (time !== null && price !== null) {
                    // Use current crosshair position if available for more accurate handle detection
                    let currentTime = time;
                    let currentPrice = price;
                    if (this._currentCrosshairPosition && this._currentCrosshairPosition.time && this._currentCrosshairPosition.price !== null) {
                        currentTime = this._currentCrosshairPosition.time;
                        currentPrice = this._currentCrosshairPosition.price;
                    }

                    const handle = this._selectedRectangle.getResizeHandle(currentTime, currentPrice);
                    this._updateCursor(handle);
                }
            }
        };

        // Add the event listeners with capture to intercept before chart
        chartElement.addEventListener('click', this._mouseClickHandler, true);
        chartElement.addEventListener('mousedown', this._mouseDownHandler, true);
        chartElement.addEventListener('mousemove', this._mouseMoveHandler, true);

        // Add mouse up handler to stop resizing
        this._mouseUpHandler = () => {
            if (this._resizing) {
                this._stopResize();
                this._enableChartInteractions();
            }
        };
        chartElement.addEventListener('mouseup', this._mouseUpHandler, true);
    }

    _setupKeyboardHandlers() {
        this._keydownHandler = (e) => {
            if (e.key === 'Escape') {
                if (this._drawing) {
                    this._cancelDrawing();
                } else if (this._selectedRectangle) {
                    this._deselectRectangle();
                }
            }
        };

        document.addEventListener('keydown', this._keydownHandler);
    }

    _cancelDrawing() {
        this._drawing = false;
        this._points = [];
        this._removePreviewRectangle();
        this._updateNavbarButton(false);
    }

    _startResize(handle) {
        this._resizing = true;
        this._resizeHandle = handle;
    }

    _stopResize() {
        this._resizing = false;
        this._resizeHandle = null;
        this._updateCursor(null);
    }

    _updateCursor(handle) {
        const chartElement = this._chart.chartElement();
        if (!chartElement) return;

        const cursorMap = {
            'top-left': 'nw-resize',
            'top-center': 'n-resize',
            'top-right': 'ne-resize',
            'middle-left': 'w-resize',
            'middle-right': 'e-resize',
            'bottom-left': 'sw-resize',
            'bottom-center': 's-resize',
            'bottom-right': 'se-resize'
        };

        const newCursor = cursorMap[handle] || 'default';
        if (chartElement.style.cursor !== newCursor) {
            chartElement.style.cursor = newCursor;
        }
    }

    _disableChartInteractions() {
        const chartElement = this._chart.chartElement();
        if (chartElement && chartElement.style.pointerEvents !== 'none') {
            this._originalPointerEvents = chartElement.style.pointerEvents;
            chartElement.style.pointerEvents = 'none';
            setTimeout(() => {
                if (chartElement && this._resizing) {
                    chartElement.style.pointerEvents = 'auto';
                }
            }, 10);
        }
    }

    _enableChartInteractions() {
        const chartElement = this._chart.chartElement();
        if (chartElement) {
            chartElement.style.pointerEvents = this._originalPointerEvents || 'auto';
        }
    }
}