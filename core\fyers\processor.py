import math
import polars as pl
from collections import deque

# Global state for candle aggregation
_candle_state = {}

def initialize_continuity_context(symbol, timeframe, bucket_size, multiplier, historical_data):
    """Initialize continuity context with the last historical candle to prevent gaps"""
    if not historical_data:
        return
    key = f"{symbol}_{timeframe}_{bucket_size}_{multiplier}"
    if key not in _candle_state:
        _candle_state[key] = CandleAggregator(timeframe, bucket_size, multiplier)
        _candle_state[key].enable_continuity_mode()
    last_candle = historical_data[-1]
    _candle_state[key].set_historical_context(symbol, last_candle)

def process_live_data(msg, timeframe, bucket_size, multiplier, ensure_continuity=True):
    """Process live tick data and return current building candle"""
    try:
        if not isinstance(msg, dict) or not msg.get('symbol') or not msg.get('ltp'):
            return None
        bucket_size = float(bucket_size)
        multiplier = int(multiplier)
        symbol = msg.get('symbol')
        key = f"{symbol}_{timeframe}_{bucket_size}_{multiplier}"
        if key not in _candle_state:
            aggregator = CandleAggregator(timeframe, bucket_size, multiplier)
            if ensure_continuity:
                aggregator.enable_continuity_mode()
            _candle_state[key] = aggregator
        return _candle_state[key].process_tick(msg)
    except Exception:
        return None

def clear_processor_state(symbol, timeframe, bucket_size, multiplier):
    """Clear processor state for a specific symbol/timeframe combination"""
    key = f"{symbol}_{timeframe}_{bucket_size}_{multiplier}"
    if key in _candle_state:
        aggregator = _candle_state[key]
        for attr in ['_candles', '_footprints', '_poc_vols', '_recent_trades', '_last_ltp']:
            if hasattr(aggregator, attr) and symbol in getattr(aggregator, attr):
                del getattr(aggregator, attr)[symbol]
        if not aggregator._candles:
            del _candle_state[key]
        return True
    return False


class CandleAggregator:
    """Aggregates live tick data into OHLCV candles with delta, POC, and footprint"""
    _interval_map = {'1m': 60, '5m': 300, '15m': 900, '1d': 86400}

    def __init__(self, timeframe, bucket_size, multiplier):
        from decimal import Decimal, getcontext
        getcontext().prec = 16
        self.timeframe = timeframe
        self.bucket_size = float(bucket_size)
        self.multiplier = int(multiplier)
        self._candles = {}
        self._footprints = {}
        self._poc_vols = {}
        self._recent_trades = {}
        self._last_ltp = {}
        self._last_cum_volume = {}
        self._last_processed_cum_volume = {}
        self._last_historical_candles = {}
        self.continuity_mode = False
        self._decimal_bucket_unit = Decimal(str(self.bucket_size * self.multiplier))




    def process_tick(self, msg):
        symbol = msg.get('symbol')
        ltp = msg.get('ltp')
        ts = msg.get('exch_feed_time') or msg.get('last_traded_time')
        
        if not symbol or ltp is None or ltp <= 0 or ts is None:
            return None
            
        try:
            ts = int(ts)
            # Normalize timestamp to seconds (handle microseconds/milliseconds)
            # Current epoch seconds ~1.7e9; ms ~1.7e12; us ~1.7e15
            if ts > 100_000_000_000_000:  # microseconds
                ts = ts // 1_000_000
            elif ts > 1_000_000_000_000:  # milliseconds
                ts = ts // 1_000
            ltp = float(ltp)
        except Exception:
            return None

        seconds = self._interval_map.get(self.timeframe, 300)
        time_bin = self._calculate_aligned_time_bin(ts, seconds)

        # --- Determine accurate trade volume (prevents duplicate aggregation) ---
        vol = self._determine_trade_volume(symbol, msg)
        print(f"Volume determined for {symbol}: {vol} (from vol_traded_today={msg.get('vol_traded_today')}, last_traded_qty={msg.get('last_traded_qty')})")
        if vol <= 0:
            print(f"Skipping tick for {symbol}: vol={vol} <= 0 (duplicate or non-trade tick)")
            return None  # No new traded volume (duplicate or non-trade tick)
        if vol > 5_000_000:  # Hard sanity cap (configurable)
            return None
        
        # Duplicate detection (fallback when cumulative volume not reliable)
        if symbol not in self._recent_trades:
            self._recent_trades[symbol] = deque(maxlen=20)
        trade_key = (ts, ltp, self._last_processed_cum_volume.get(symbol, 0))
        if trade_key in self._recent_trades[symbol]:
            return None
        self._recent_trades[symbol].append(trade_key)

        # Check for volume increase without LTP change (normal in many cases, but monitored)
        last_ltp = self._last_ltp.get(symbol)
        if last_ltp is not None and abs(ltp - last_ltp) < 0.01 and vol > 0:
            # Volume is being added but price hasn't changed significantly
            # This is normal for multiple trades at same price, but monitor for suspicious patterns
            current_candle = self._candles.get(symbol)
            if current_candle and current_candle.get('time') == time_bin:
                # Multiple trades in same candle at same price - this is normal
                pass
            else:
                # New candle but same price as previous - also normal but worth noting
                pass
        
        # Update last LTP for this symbol
        self._last_ltp[symbol] = ltp
        
        # Calculate buy/sell volume (allocation must sum to vol) using refined aggressor logic
        buy, sell = self._calculate_buy_sell_volume(msg, vol)
        if buy + sell != vol:  # Final guard
            diff = vol - (buy + sell)
            if buy >= sell:
                buy += diff
            else:
                sell += diff
        delta = buy - sell

        # Determine price bucket using high precision quantization
        bucket = self._get_bucket_key(ltp)

        # Handle candle creation/update
        c = self._candles.get(symbol)
        is_new_candle = (c is None or c['time'] != time_bin)
        
        if is_new_candle:
            if self._should_continue_historical_candle(symbol, time_bin, ltp):
                self._candles[symbol] = self._initialize_from_historical(symbol, time_bin, ltp, vol, buy, sell)
                if symbol in self._last_historical_candles:
                    del self._last_historical_candles[symbol]
            else:
                daily_open = msg.get('open_price')
                candle_open = daily_open if daily_open and self._is_first_candle_of_day(time_bin) else ltp
                
                self._candles[symbol] = {
                    'time': time_bin,
                    'open': candle_open,
                    'high': ltp,
                    'low': ltp,
                    'close': ltp,
                    'volume': vol,
                    'buy_vol': buy,
                    'sell_vol': sell,
                    'delta': delta,
                    'poc': ltp,
                    'footprint': []
                }
                
            self._footprints[symbol] = {bucket: {'buy': buy, 'sell': sell}}
            self._poc_vols[symbol] = {bucket: buy + sell}
        else:
            c['high'] = max(c['high'], ltp)
            c['low'] = min(c['low'], ltp)
            c['close'] = ltp
            c['volume'] += vol
            c['buy_vol'] += buy
            c['sell_vol'] += sell
            c['delta'] += delta
            
            fp = self._footprints[symbol]
            if bucket not in fp:
                fp[bucket] = {'buy': 0, 'sell': 0}
            fp[bucket]['buy'] += buy
            fp[bucket]['sell'] += sell
            
            pocv = self._poc_vols[symbol]
            if bucket not in pocv:
                pocv[bucket] = 0
            pocv[bucket] += buy + sell

        # Update POC and footprint
        c = self._candles[symbol]
        poc_map = self._poc_vols[symbol]
        
        if poc_map:
            try:
                c['poc'] = max(poc_map.items(), key=lambda x: x[1])[0]
            except (ValueError, KeyError):
                c['poc'] = c['close']
        else:
            c['poc'] = c['close']
            
        # Create footprint
        fp_map = self._footprints[symbol]
        c['footprint'] = self._build_footprint(c, fp_map)
            
        return c.copy()

    # ----------------- Helper / Internal Methods -----------------
    def _determine_trade_volume(self, symbol, msg):
        """Determine per-tick trade volume with fallbacks.
        Priority:
          1. last_traded_qty if present & sane (>0)
          2. Difference in cumulative vol_traded_today
        Ensures non-negative integer volume.
        """
        raw_trade = msg.get('last_traded_qty')
        cum = msg.get('vol_traded_today')

        # Track latest observed cumulative (even if not processed due to being stale)
        if isinstance(cum, (int, float)) and cum >= 0:
            self._last_cum_volume[symbol] = int(cum)

        # Prefer cumulative delta for de-duplication when available
        if isinstance(cum, (int, float)) and cum >= 0:
            cur_cum = int(cum)
            last_proc = self._last_processed_cum_volume.get(symbol)
            if last_proc is None:
                # First tick: cannot compute delta; fall back to raw trade if sane
                if isinstance(raw_trade, (int, float)) and raw_trade > 0:
                    self._last_processed_cum_volume[symbol] = cur_cum
                    return int(raw_trade)
                else:
                    self._last_processed_cum_volume[symbol] = cur_cum
                    return 0
            # Handle reset (e.g., new session) where cumulative shrinks
            if cur_cum < last_proc:
                self._last_processed_cum_volume[symbol] = cur_cum
                if isinstance(raw_trade, (int, float)) and raw_trade > 0:
                    return int(raw_trade)
                return 0
            delta = cur_cum - last_proc
            if delta <= 0:
                return 0  # No new traded volume
            # Sanity threshold
            if delta > 2_000_000:
                return 0
            self._last_processed_cum_volume[symbol] = cur_cum
            return int(delta)

        # Fallback: no cumulative available; rely on last_traded_qty
        if isinstance(raw_trade, (int, float)) and raw_trade > 0:
            return int(raw_trade)
        return 0

    def _get_bucket_key(self, price):
        """Quantize price to bucket using Decimal to avoid float drift."""
        from decimal import Decimal, ROUND_FLOOR
        try:
            p = Decimal(str(price))
            unit = self._decimal_bucket_unit
            if unit == 0:
                return float(round(price, 2))
            # Use floor-style bucketing to match historical processing (// bucket)
            buckets = (p / unit).to_integral_value(rounding=ROUND_FLOOR)
            bucket_price = (buckets * unit).quantize(Decimal('0.01'))
            return float(bucket_price)
        except Exception:
            # Fallback
            bucket_value = self.bucket_size * self.multiplier
            return round(round(price / bucket_value) * bucket_value, 2)

    def _build_footprint(self, candle, fp_map):
        """Construct ordered footprint list from footprint map."""
        bucket_value = self.bucket_size * self.multiplier
        if bucket_value <= 0:
            return []

        # If we have no volume map, return empty
        if not fp_map:
            return []

        # Build from the first traded bucket to the last traded bucket to avoid
        # adding zero-only buckets above/below the traded range.
        try:
            traded_levels = sorted(fp_map.keys())
        except Exception:
            # In case keys aren't sortable for any reason
            traded_levels = list(fp_map.keys())
        if not traded_levels:
            return []

        min_traded = round(min(traded_levels), 2)
        max_traded = round(max(traded_levels), 2)

        # Defensive guard to prevent runaway loops
        max_iters = 5000
        iters = 0
        ladder = []
        current = min_traded
        # Normalize step to two decimals to avoid float drift in loop increments
        step = round(bucket_value, 6)
        while current <= max_traded + 1e-9 and iters < max_iters:
            level = round(current, 2)
            entry = fp_map.get(level, {'buy': 0, 'sell': 0})
            ladder.append({
                'priceLevel': level,
                'buyVolume': int(entry.get('buy', 0)),
                'sellVolume': int(entry.get('sell', 0))
            })
            current = round(current + step, 6)
            iters += 1

        # Sort descending by price
        ladder = sorted(ladder, key=lambda x: x['priceLevel'], reverse=True)

        # Reconcile footprint to exactly match candle buy/sell totals (thus total and delta)
        try:
            # Current totals
            cur_buy = sum(int(it.get('buyVolume', 0)) for it in ladder)
            cur_sell = sum(int(it.get('sellVolume', 0)) for it in ladder)

            # Target totals: prefer candle's buy/sell if present; otherwise derive from volume/delta
            if 'buy_vol' in candle and 'sell_vol' in candle:
                tgt_buy = int(candle.get('buy_vol') or 0)
                tgt_sell = int(candle.get('sell_vol') or 0)
            else:
                tgt_total = int(candle.get('volume', cur_buy + cur_sell) or (cur_buy + cur_sell))
                tgt_delta = int(candle.get('delta', cur_buy - cur_sell) or (cur_buy - cur_sell))
                tb = tgt_total + tgt_delta
                ts = tgt_total - tgt_delta
                tgt_buy = tb // 2
                tgt_sell = ts // 2
                # Handle odd parity by assigning remainder to buy
                if tb % 2 != 0:
                    tgt_buy += 1

            # Adjust buy side to target
            d_buy = tgt_buy - cur_buy
            if d_buy != 0 and ladder:
                if d_buy > 0:
                    # Add to bucket with largest combined volume (minimal distortion)
                    idxs = sorted(range(len(ladder)), key=lambda i: (ladder[i]['buyVolume'] + ladder[i]['sellVolume']), reverse=True)
                    i0 = idxs[0]
                    ladder[i0]['buyVolume'] = int(ladder[i0]['buyVolume']) + int(d_buy)
                else:
                    # Remove from buckets with largest buy first
                    remaining = -int(d_buy)
                    idxs = sorted(range(len(ladder)), key=lambda i: ladder[i]['buyVolume'], reverse=True)
                    for i in idxs:
                        if remaining <= 0:
                            break
                        b = int(ladder[i]['buyVolume'])
                        if b <= 0:
                            continue
                        take = min(b, remaining)
                        ladder[i]['buyVolume'] = b - take
                        remaining -= take

            # Recompute cur_sell in case buy changes are not supposed to affect sell totals
            cur_sell = sum(int(it.get('sellVolume', 0)) for it in ladder)
            # Adjust sell side to target
            d_sell = tgt_sell - cur_sell
            if d_sell != 0 and ladder:
                if d_sell > 0:
                    idxs = sorted(range(len(ladder)), key=lambda i: (ladder[i]['buyVolume'] + ladder[i]['sellVolume']), reverse=True)
                    i0 = idxs[0]
                    ladder[i0]['sellVolume'] = int(ladder[i0]['sellVolume']) + int(d_sell)
                else:
                    remaining = -int(d_sell)
                    idxs = sorted(range(len(ladder)), key=lambda i: ladder[i]['sellVolume'], reverse=True)
                    for i in idxs:
                        if remaining <= 0:
                            break
                        s = int(ladder[i]['sellVolume'])
                        if s <= 0:
                            continue
                        take = min(s, remaining)
                        ladder[i]['sellVolume'] = s - take
                        remaining -= take
        except Exception:
            # If anything goes wrong, keep ladder as-is
            pass

        return ladder

    def _calculate_buy_sell_volume(self, msg, vol):
        """Allocate trade volume into buy/sell based on aggressor logic.

        Heuristic priority:
          1. Trade at/through ask => all buy (aggressive buy)
          2. Trade at/through bid => all sell (aggressive sell)
          3. Inside spread or missing book: use order book imbalance (tot_buy_qty / (tot_buy_qty+tot_sell_qty))
          4. Fallback: price change direction or 50/50.
        """
        if vol <= 0:
            return 0, 0
        ltp = msg.get('ltp')
        bid = msg.get('bid_price')
        ask = msg.get('ask_price')
        tot_buy_qty = msg.get('tot_buy_qty')
        tot_sell_qty = msg.get('tot_sell_qty')
        eps = 1e-6
        buy = sell = 0

        if bid is not None and ask is not None and ltp is not None:
            # Aggressor detection
            if ask >= bid:  # Valid book
                if ltp >= ask - eps:  # Hit/through ask
                    buy = vol
                    sell = 0
                elif ltp <= bid + eps:  # Hit/through bid
                    sell = vol
                    buy = 0
                else:
                    # Inside spread: use imbalance if available
                    if isinstance(tot_buy_qty, (int, float)) and isinstance(tot_sell_qty, (int, float)) and (tot_buy_qty + tot_sell_qty) > 0:
                        total_pressure = tot_buy_qty + tot_sell_qty
                        buy_ratio = tot_buy_qty / total_pressure
                        buy = int(round(vol * buy_ratio))
                        sell = vol - buy
                    else:
                        # Neutral inside spread
                        buy = vol // 2
                        sell = vol - buy
            else:
                # Invalid book (negative spread): fallback to imbalance or price change
                if isinstance(tot_buy_qty, (int, float)) and isinstance(tot_sell_qty, (int, float)) and (tot_buy_qty + tot_sell_qty) > 0:
                    total_pressure = tot_buy_qty + tot_sell_qty
                    buy_ratio = tot_buy_qty / total_pressure
                    buy = int(round(vol * buy_ratio))
                    sell = vol - buy
        # If still unassigned use order book imbalance directly
        if buy + sell == 0 and isinstance(tot_buy_qty, (int, float)) and isinstance(tot_sell_qty, (int, float)) and (tot_buy_qty + tot_sell_qty) > 0:
            total_pressure = tot_buy_qty + tot_sell_qty
            buy_ratio = tot_buy_qty / total_pressure
            buy = int(round(vol * buy_ratio))
            sell = vol - buy
        # Fallback to price change direction
        if buy + sell == 0:
            price_change = msg.get('ch')
            if isinstance(price_change, (int, float)):
                if price_change > 0:
                    buy = vol
                elif price_change < 0:
                    sell = vol
                else:
                    buy = vol // 2
                    sell = vol - buy
            else:
                buy = vol // 2
                sell = vol - buy
        # Final guard to ensure conservation
        if buy + sell != vol:
            # Adjust by assigning remainder to side of prevailing direction
            remainder = vol - (buy + sell)
            if abs(remainder) > 0:
                if buy >= sell:
                    buy += remainder
                else:
                    sell += remainder
        return int(buy), int(sell)
    
    def enable_continuity_mode(self):
        """Enable continuity mode for seamless historical-to-live transition"""
        self.continuity_mode = True
    
    def set_historical_context(self, symbol, last_historical_candle):
        """Set the last historical candle to ensure continuity with live data"""
        if self.continuity_mode:
            self._last_historical_candles[symbol] = last_historical_candle
    
    def _should_continue_historical_candle(self, symbol, time_bin, ltp):
        """Check if current tick should continue the last historical candle"""
        if not self.continuity_mode or symbol not in self._last_historical_candles:
            return False
        
        last_hist = self._last_historical_candles[symbol]
        
        if last_hist.get('time') == time_bin:
            return True
        
        seconds = self._interval_map.get(self.timeframe, 300)
        
        # Calculate buffer based on timeframe
        if self.timeframe == '1m':
            buffer_seconds = 15
        elif self.timeframe == '5m':
            buffer_seconds = 60
        elif self.timeframe == '15m':
            buffer_seconds = 180
        else:
            buffer_seconds = 30
        
        time_diff = time_bin - last_hist.get('time', 0)
        
        if 0 < time_diff <= buffer_seconds and time_diff <= seconds:
            return True
        
        if seconds < time_diff < 86400:
            expected_next_bin = last_hist.get('time', 0) + seconds
            if time_bin >= expected_next_bin:
                return True
            
        return False
    
    def _initialize_from_historical(self, symbol, time_bin, ltp, vol, buy, sell):
        """Initialize candle using historical context"""
        last_hist = self._last_historical_candles[symbol]
        
        return {
            'time': time_bin,
            'open': last_hist.get('close', ltp),
            'high': max(last_hist.get('high', ltp), ltp),
            'low': min(last_hist.get('low', ltp), ltp),
            'close': ltp,
            'volume': last_hist.get('volume', 0) + vol,
            'buy_vol': last_hist.get('buy_vol', 0) + buy,
            'sell_vol': last_hist.get('sell_vol', 0) + sell,
            'delta': last_hist.get('delta', 0) + (buy - sell),
            'poc': ltp,
            'footprint': []
        }

    def _calculate_aligned_time_bin(self, timestamp, interval_seconds):
        """Calculate time bin aligned with market hours"""
        from datetime import datetime, time
        
        try:
            import pytz
            ist = pytz.timezone('Asia/Kolkata')
            dt = datetime.fromtimestamp(timestamp, tz=ist)
            use_timezone = True
        except ImportError:
            dt = datetime.fromtimestamp(timestamp)
            use_timezone = False
        
        market_open_time = time(9, 15, 0)
        market_open_dt = datetime.combine(dt.date(), market_open_time)
        if use_timezone and hasattr(dt, 'tzinfo') and dt.tzinfo:
            market_open_dt = market_open_dt.replace(tzinfo=dt.tzinfo)
        
        market_open_timestamp = int(market_open_dt.timestamp())
        
        if timestamp < market_open_timestamp:
            time_diff = market_open_timestamp - timestamp
            if time_diff >= 86400:
                return (timestamp // interval_seconds) * interval_seconds
            return (timestamp // interval_seconds) * interval_seconds
        
        seconds_since_open = timestamp - market_open_timestamp
        candle_period = seconds_since_open // interval_seconds
        aligned_timestamp = market_open_timestamp + (candle_period * interval_seconds)
        
        return int(aligned_timestamp)

    def _is_first_candle_of_day(self, time_bin):
        """Check if this is the first candle of the trading day"""
        from datetime import datetime
        dt = datetime.fromtimestamp(time_bin)
        
        if self.timeframe in ['1m', '5m', '15m']:
            return dt.hour == 9 and dt.minute == 15
        else:
            return dt.hour == 9 and dt.minute <= 20



# Process historical data ..........................................................................................................

def process_hist_data(df, timeframe, value_area_pct=0.7, symbol_col=None, data_frame=False, footprint=True, bucket_size=0.05, multiplier=100):
    """Efficiently process historical OHLCV dataframe for delta candles, value area, and POC"""
    if not isinstance(df, pl.DataFrame):
        raise ValueError('Input must be a Polars DataFrame')
    if 'timestamp' not in df.columns:
        raise ValueError('DataFrame must have a timestamp column')

    # Interval to seconds mapping
    interval_map = {'1m': 60, '5m': 300, '15m': 900, '1d': 86400}
    seconds = interval_map.get(timeframe, 300)

    # Setup columns
    duplicate_cols = ['timestamp']
    group_cols = ['time']
    if symbol_col and symbol_col in df.columns:
        duplicate_cols.append(symbol_col)
        group_cols.append(symbol_col)
    poc_group_cols = group_cols.copy()

    # Main processing pipeline
    # Reusable buy/sell volume expressions (heuristic proxy for delta)
    buy_expr = (
        pl.when(pl.col('close') > pl.col('open')).then(pl.col('volume'))
          .when((pl.col('close') == pl.col('open')) & (pl.col('close') > pl.col('close').shift(1)))
          .then(pl.col('volume'))
          .otherwise(0)
    )
    sell_expr = (
        pl.when(pl.col('close') < pl.col('open')).then(pl.col('volume'))
          .when((pl.col('close') == pl.col('open')) & (pl.col('close') < pl.col('close').shift(1)))
          .then(pl.col('volume'))
          .otherwise(0)
    )

    ldf = (
        df.lazy()
        .with_columns([
            pl.col(symbol_col).cast(pl.Categorical) if symbol_col and symbol_col in df.columns else pl.lit(None),
            pl.col('timestamp').cast(pl.Int64),
        ])
        .unique(subset=duplicate_cols, keep='first')
        .with_columns([(pl.col('timestamp') // seconds * seconds).alias('time')])
        .sort([symbol_col, 'timestamp'] if symbol_col and symbol_col in df.columns else ['timestamp'])
        .with_columns([
            buy_expr.alias('buy_vol'),
            sell_expr.alias('sell_vol')
        ])
    )

    # Aggregation
    agg = [
        pl.col('open').first().alias('open'),
        pl.col('high').max().alias('high'),
        pl.col('low').min().alias('low'),
        pl.col('close').last().alias('close'),
        pl.col('volume').sum().alias('volume'),
        pl.col('buy_vol').sum().alias('buy_vol'),
        pl.col('sell_vol').sum().alias('sell_vol')
    ]
    
    grouped = ldf.group_by(group_cols).agg(agg).with_columns([
        (pl.col('buy_vol') - pl.col('sell_vol')).alias('delta')
    ])

    # POC calculation
    price_vol = ldf.group_by(poc_group_cols + ['close']).agg(pl.col('volume').sum().alias('vol_by_price'))
    poc_df = price_vol.sort(group_cols + ['vol_by_price'], descending=[False]*len(group_cols) + [True]).group_by(poc_group_cols).agg([
        pl.col('close').first().alias('poc')
    ])
    
    result = grouped.join(poc_df, on=poc_group_cols, how='left')
    out_cols = group_cols + ['open', 'high', 'low', 'close', 'volume', 'buy_vol', 'sell_vol', 'delta', 'poc']
    output_df = result.select(out_cols).collect().sort('time').unique(subset=['time'], keep='first')

    # Footprint calculation (refined: skip empty buckets & avoid overshoot above high)
    if footprint:
        bucket = bucket_size * multiplier
        if bucket <= 0:
            if data_frame:
                return output_df
            return clean_nans(output_df.to_dicts())

        df_exp = (
            df.lazy()
              .with_columns([
                  ((pl.col('timestamp') // seconds) * seconds).alias('time'),
                  ((pl.col('close') // bucket) * bucket).round(2).alias('price_bucket'),
                  buy_expr.alias('buy_vol'),
                  sell_expr.alias('sell_vol')
              ])
              .collect()
        )

        fp_group_cols = ['time', 'price_bucket']
        if symbol_col and symbol_col in df.columns:
            fp_group_cols.append(symbol_col)

        fp_df = (
            df_exp.group_by(fp_group_cols)
                  .agg([
                      pl.col('buy_vol').sum().alias('buyVolume'),
                      pl.col('sell_vol').sum().alias('sellVolume')
                  ])
                  .sort(['time', 'price_bucket'])
        )

        # Build lookup
        fp_dict = {}
        sym_present = symbol_col and symbol_col in fp_df.columns
        for row in fp_df.iter_rows(named=True):
            key = (row['time'], row[symbol_col]) if sym_present else (row['time'],)
            fp_dict.setdefault(key, {})[float(row['price_bucket'])] = {
                'buyVolume': int(row['buyVolume']),
                'sellVolume': int(row['sellVolume'])
            }

        out_rows = output_df.to_dicts()
        for r in out_rows:
            key = (r['time'], r.get(symbol_col)) if (symbol_col and symbol_col in r) else (r['time'],)
            high = r['high']
            low = r['low']
            # Use floor for bounds to avoid creating a synthetic bucket above the high
            min_bucket = math.floor(low / bucket) * bucket
            max_bucket = math.floor(high / bucket) * bucket
            buckets = []
            b = min_bucket
            iters = 0
            while b <= max_bucket + 1e-9 and iters < 5000:
                level = round(b, 2)
                d = fp_dict.get(key, {}).get(level)
                if d:  # only include levels where we actually aggregated volume
                    buckets.append({'priceLevel': level, 'buyVolume': d['buyVolume'], 'sellVolume': d['sellVolume']})
                b = round(b + bucket, 8)
                iters += 1
            # Sort descending (common footprint convention)
            buckets = sorted(buckets, key=lambda x: x['priceLevel'], reverse=True)

            # Reconcile footprint to exactly match candle buy/sell totals (thus total and delta)
            try:
                # Current totals
                cur_buy = sum(int(it.get('buyVolume', 0)) for it in buckets)
                cur_sell = sum(int(it.get('sellVolume', 0)) for it in buckets)

                # Target totals: prefer grouped candle's buy/sell if present; else derive
                if 'buy_vol' in r and 'sell_vol' in r:
                    tgt_buy = int(r.get('buy_vol') or 0)
                    tgt_sell = int(r.get('sell_vol') or 0)
                else:
                    tgt_total = int(r.get('volume', cur_buy + cur_sell) or (cur_buy + cur_sell))
                    tgt_delta = int(r.get('delta', cur_buy - cur_sell) or (cur_buy - cur_sell))
                    tb = tgt_total + tgt_delta
                    ts = tgt_total - tgt_delta
                    tgt_buy = tb // 2
                    tgt_sell = ts // 2
                    if tb % 2 != 0:
                        tgt_buy += 1

                # Adjust buy side to target
                d_buy = tgt_buy - cur_buy
                if d_buy != 0 and buckets:
                    if d_buy > 0:
                        idxs = sorted(range(len(buckets)), key=lambda i: (buckets[i]['buyVolume'] + buckets[i]['sellVolume']), reverse=True)
                        i0 = idxs[0]
                        buckets[i0]['buyVolume'] = int(buckets[i0]['buyVolume']) + int(d_buy)
                    else:
                        remaining = -int(d_buy)
                        idxs = sorted(range(len(buckets)), key=lambda i: buckets[i]['buyVolume'], reverse=True)
                        for i in idxs:
                            if remaining <= 0:
                                break
                            b = int(buckets[i]['buyVolume'])
                            if b <= 0:
                                continue
                            take = min(b, remaining)
                            buckets[i]['buyVolume'] = b - take
                            remaining -= take

                # Recompute cur_sell and adjust sell side
                cur_sell = sum(int(it.get('sellVolume', 0)) for it in buckets)
                d_sell = tgt_sell - cur_sell
                if d_sell != 0 and buckets:
                    if d_sell > 0:
                        idxs = sorted(range(len(buckets)), key=lambda i: (buckets[i]['buyVolume'] + buckets[i]['sellVolume']), reverse=True)
                        i0 = idxs[0]
                        buckets[i0]['sellVolume'] = int(buckets[i0]['sellVolume']) + int(d_sell)
                    else:
                        remaining = -int(d_sell)
                        idxs = sorted(range(len(buckets)), key=lambda i: buckets[i]['sellVolume'], reverse=True)
                        for i in idxs:
                            if remaining <= 0:
                                break
                            s = int(buckets[i]['sellVolume'])
                            if s <= 0:
                                continue
                            take = min(s, remaining)
                            buckets[i]['sellVolume'] = s - take
                            remaining -= take
            except Exception:
                pass

            r['footprint'] = buckets

        if data_frame:
            import pandas as pd
            return pd.DataFrame(out_rows)
        return clean_nans(out_rows)

    if data_frame:
        return output_df
    else:
        return clean_nans(output_df.to_dicts())
    
def clean_nans(obj):
    """Replace NaN, None, and inf values with None for JSON serialization"""
    if isinstance(obj, float):
        if math.isnan(obj) or math.isinf(obj):
            return None
        return obj
    if obj is None:
        return None
    if isinstance(obj, dict):
        return {k: clean_nans(v) for k, v in obj.items()}
    if isinstance(obj, list):
        return [clean_nans(x) for x in obj]
    return obj
