# Crosshair-Based Rectangle Drawing Enhancement

## Overview
Enhanced the rectangle drawing tool to use crosshair crossover points as reference instead of raw mouse cursor position when drawing rectangles. This provides more accurate positioning in ALL crosshair modes (magnet, normal, etc.) by always using the exact crosshair position.

## Changes Made

### 1. Added Crosshair Position Tracking
- Added `_currentCrosshairPosition` property to store the current crosshair position
- This position is updated on every crosshair move event

### 2. Created Helper Method `_getCrosshairPosition(param)`
- Extracts crosshair position from crosshair move/click event parameters
- Prioritizes series data points when crosshair is in magnet mode
- Falls back to coordinate-based positioning when no series data is available
- Handles different data point types (candlestick, line series, etc.)

### 3. Updated Event Handlers
- **`_onClick(param)`**: Now uses crosshair position for all drawing operations
- **`_onMouseMove(param)`**: Uses crosshair position for preview updates and resizing
- **Mouse event handlers**: Updated to use crosshair position for consistency

### 4. Enhanced Position Detection Logic
The tool now follows this priority order for position detection:

**For Time:**
1. **param.time** (crosshair's exact time position)
2. **Coordinate-based time** (calculated from param.point.x if param.time unavailable)

**For Price:**
1. **Series data point** (when crosshair snaps to data in any mode)
   - Uses `dataPoint.close` for candlestick data
   - Uses `dataPoint.value` for line series data
   - Falls back to `dataPoint.high` or `dataPoint.low` for candlestick data
2. **Coordinate-based price** (calculated from param.point.y - works in all modes)

## Benefits

### 1. Improved Accuracy
- Rectangles are drawn using exact crosshair crossover points
- Eliminates discrepancies between visual crosshair position and actual drawing coordinates

### 2. Universal Crosshair Mode Support
- Works with ALL crosshair modes (magnet, normal, etc.)
- Automatically snaps rectangle corners to data points when crosshair does (magnet mode)
- Uses exact crosshair position even in normal mode

### 3. Consistent Behavior
- All drawing operations (start point, end point, resizing) use the same position logic
- Provides predictable and intuitive drawing experience

### 4. Backward Compatibility
- Maintains fallback to coordinate-based positioning
- Works with all chart types and crosshair modes

## Technical Details

### Crosshair Position Detection
```javascript
// Check if crosshair is snapped to a data point (magnet mode)
if (param.seriesData && param.seriesData.has(this._series)) {
    const dataPoint = param.seriesData.get(this._series);
    if (dataPoint && dataPoint.time !== undefined) {
        time = dataPoint.time;
        // Use appropriate price based on data point type
        if (dataPoint.close !== undefined) {
            price = dataPoint.close; // For candlestick data
        } else if (dataPoint.value !== undefined) {
            price = dataPoint.value; // For line series data
        }
    }
}
```

### Usage in Drawing Operations
- **Rectangle start point**: Uses crosshair position from click event
- **Rectangle end point**: Uses crosshair position from move events during drawing
- **Resize operations**: Uses crosshair position for handle detection and resizing

## Files Modified
- `static/js/drawing-tool.js`: Main implementation file with all enhancements

## Testing Recommendations
1. Test drawing rectangles with crosshair in magnet mode
2. Verify rectangles snap to data points when crosshair does
3. Test with different chart types (candlestick, line, etc.)
4. Verify fallback behavior when crosshair is not in magnet mode
5. Test resize operations with crosshair-based positioning
