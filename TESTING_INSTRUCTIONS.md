# Testing Instructions for Crosshair-Based Rectangle Drawing

## How to Test the Enhancement

### 1. Open the Application
- Load your TradingView chart application
- Ensure the rectangle drawing tool is available in the toolbar

### 2. Test in Different Crosshair Modes

#### Test A: Magnet Mode (Default)
1. Ensure crosshair is in magnet mode (mode: 0 in chart options)
2. Activate the rectangle drawing tool
3. Move crosshair over the chart - notice it snaps to data points
4. <PERSON>lick to start drawing a rectangle
5. Move to another position and click to complete
6. **Expected Result**: Rectangle corners should be exactly at the data points where crosshair snapped

#### Test B: Normal Mode
1. Change crosshair to normal mode (mode: 1 in chart options)
2. Activate the rectangle drawing tool
3. Move crosshair over the chart - it should move freely
4. <PERSON>lick to start drawing a rectangle
5. Move to another position and click to complete
6. **Expected Result**: Rectangle corners should be exactly at crosshair position, not mouse cursor

### 3. Debug Information
- Open browser console (F12)
- Look for debug messages starting with `[CLICK]` and `[MOVE]`
- These will show you what position data is being used

### 4. Visual Verification
- Compare where you see the crosshair vs where rectangle corners are placed
- They should match exactly in both magnet and normal modes
- In magnet mode, rectangles should snap to data points
- In normal mode, rectangles should follow crosshair precisely

### 5. Test Different Chart Types
- Test with candlestick charts
- Test with line charts
- Test with different timeframes

## What to Look For

### ✅ Success Indicators
- Rectangle corners appear exactly where crosshair is positioned
- In magnet mode: rectangles snap to OHLC data points
- In normal mode: rectangles follow crosshair smoothly
- No discrepancy between visual crosshair and rectangle placement

### ❌ Issues to Report
- Rectangle corners don't match crosshair position
- Tool only works in one crosshair mode
- Console errors in browser
- Rectangles appear at mouse cursor instead of crosshair

## Troubleshooting

### If rectangles don't follow crosshair:
1. Check browser console for errors
2. Verify crosshair events are firing
3. Check if `param.time` and `param.point` are available in events

### If only works in magnet mode:
1. Check if coordinate-based fallback is working
2. Verify `this._series.coordinateToPrice()` is functioning

## Debug Commands
You can run these in browser console while testing:

```javascript
// Check current crosshair mode
chart.options().crosshair.mode

// Check if drawing tool exists
window.rectangleDrawingTool

// Enable more detailed logging (if needed)
window.rectangleDrawingTool._debugMode = true;
```
